import router from './router'
import store from './store'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

NProgress.configure({ showSpinner: false })

router.beforeEach((to, from, next) => {
  NProgress.start()
  // 设置页面标题
  to.meta.title && store.dispatch('settings/setTitle', to.meta.title)

  // 如果访问登录页面，直接跳转到首页
  if (to.path === '/login') {
    next({ path: '/' })
    NProgress.done()
    return
  }

  // 直接允许所有路由访问，不进行登录验证
  next()
})

router.afterEach(() => {
  NProgress.done()
})
